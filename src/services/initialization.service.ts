import { createSignal } from "solid-js";
import {
  InitializationConfig,
  InitializationErrorCallback,
  InitializationProgressCallback,
  InitializationState,
  InitializationStatus,
  InitializationStep,
  InitializationStepInfo,
  StepExecutor
} from "~/types/initialization.types";
import { logDebug } from "~/util/logger";

/**
 * Initialization Service - Manages the app startup sequence to prevent race conditions
 */
export default function InitializationService() {

  const [state, setState] = createSignal<InitializationState>({
    steps: new Map(),
    isComplete: false,
    hasErrors: false,
    startTime: Date.now()
  });

  const [progressCallback, setProgressCallback] = createSignal<InitializationProgressCallback>();
  const [errorCallback, setErrorCallback] = createSignal<InitializationErrorCallback>();

  const config: InitializationConfig = {
    defaultTimeout: 30000, // 30 seconds
    maxRetries: 3,
    retryDelay: 1000,
    enableLogging: true
  };

  // Define step dependencies
  const stepDependencies: Record<InitializationStep, InitializationStep[]> = {
    [InitializationStep.UserGesture]: [],
    [InitializationStep.CoreWasm]: [InitializationStep.UserGesture],
    [InitializationStep.AppState]: [InitializationStep.CoreWasm],
    [InitializationStep.WebsocketIdentity]: [InitializationStep.AppState],
    [InitializationStep.WebsocketConnection]: [InitializationStep.WebsocketIdentity],
    [InitializationStep.WelcomeEvent]: [InitializationStep.WebsocketConnection],
    [InitializationStep.ClientLoaded]: [InitializationStep.WelcomeEvent],
    [InitializationStep.AudioService]: [InitializationStep.ClientLoaded],
    [InitializationStep.SynthEngine]: [InitializationStep.AudioService],
    [InitializationStep.ClientSocketId]: [InitializationStep.ClientLoaded],
    [InitializationStep.ClientAddedToSynth]: [InitializationStep.SynthEngine, InitializationStep.ClientSocketId],
    [InitializationStep.Soundfont]: [InitializationStep.ClientAddedToSynth],
    [InitializationStep.AppSettings]: [InitializationStep.ClientAddedToSynth],
    [InitializationStep.UsersService]: [InitializationStep.AppSettings],
    [InitializationStep.ChatService]: [InitializationStep.UsersService],
    [InitializationStep.RoomsService]: [InitializationStep.ChatService],
    [InitializationStep.MonitorService]: [InitializationStep.RoomsService],
    [InitializationStep.Complete]: [InitializationStep.MonitorService, InitializationStep.Soundfont]
  };

  const initializeSteps = () => {
    const steps = new Map<InitializationStep, InitializationStepInfo>();

    Object.values(InitializationStep).forEach(step => {
      steps.set(step, {
        step,
        status: InitializationStatus.Pending,
        dependencies: stepDependencies[step] || [],
        timeout: config.defaultTimeout,
        retryCount: 0,
        maxRetries: config.maxRetries
      });
    });

    setState(prev => ({ ...prev, steps }));
  };

  const areDepencenciesMet = (step: InitializationStep): boolean => {
    const stepInfo = state().steps.get(step);
    if (!stepInfo) return false;

    return stepInfo.dependencies.every(dep => {
      const depInfo = state().steps.get(dep);
      return depInfo?.status === InitializationStatus.Completed;
    });
  };

  const updateStepStatus = (step: InitializationStep, status: InitializationStatus, error?: string) => {
    setState(prev => {
      const newSteps = new Map(prev.steps);
      const stepInfo = newSteps.get(step);

      if (stepInfo) {
        stepInfo.status = status;
        stepInfo.error = error;

        if (status === InitializationStatus.InProgress) {
          stepInfo.startTime = Date.now();
        } else if (status === InitializationStatus.Completed || status === InitializationStatus.Failed) {
          stepInfo.endTime = Date.now();
        }

        newSteps.set(step, stepInfo);
      }

      const hasErrors = Array.from(newSteps.values()).some(s => s.status === InitializationStatus.Failed);
      const isComplete = step === InitializationStep.Complete && status === InitializationStatus.Completed;

      return {
        ...prev,
        steps: newSteps,
        currentStep: status === InitializationStatus.InProgress ? step : prev.currentStep,
        hasErrors,
        isComplete
      };
    });

    // Notify callbacks
    const progress = calculateProgress();
    progressCallback()?.(step, status, progress);

    if (status === InitializationStatus.Failed && error) {
      errorCallback()?.(step, error);
    }

    if (config.enableLogging) {
      logDebug(`[InitializationService] Step ${step}: ${status}${error ? ` - ${error}` : ''}`);
    }
  };

  const calculateProgress = (): number => {
    const steps = Array.from(state().steps.values());
    const completedSteps = steps.filter(s => s.status === InitializationStatus.Completed).length;
    return Math.round((completedSteps / steps.length) * 100);
  };

  const executeStep = async (step: InitializationStep, executor: StepExecutor): Promise<void> => {
    const stepInfo = state().steps.get(step);
    if (!stepInfo) {
      throw new Error(`Step ${step} not found`);
    }

    if (!areDepencenciesMet(step)) {
      throw new Error(`Dependencies not met for step ${step}`);
    }

    if (stepInfo.status === InitializationStatus.Completed) {
      return; // Already completed
    }

    updateStepStatus(step, InitializationStatus.InProgress);

    try {
      // Execute with timeout
      await Promise.race([
        executor.execute(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error(`Timeout after ${stepInfo.timeout}ms`)), stepInfo.timeout)
        )
      ]);

      // Validate if validator is provided
      if (executor.validate) {
        const isValid = await executor.validate();
        if (!isValid) {
          throw new Error(`Validation failed for step ${step}`);
        }
      }

      updateStepStatus(step, InitializationStatus.Completed);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      if (stepInfo.retryCount < stepInfo.maxRetries) {
        stepInfo.retryCount++;
        updateStepStatus(step, InitializationStatus.Retrying, errorMessage);

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, config.retryDelay));

        // Retry
        return executeStep(step, executor);
      } else {
        updateStepStatus(step, InitializationStatus.Failed, errorMessage);
        throw error;
      }
    }
  };

  const waitForStep = async (step: InitializationStep, timeout?: number): Promise<void> => {
    const timeoutMs = timeout || config.defaultTimeout;

    await Promise.race([
      until(() => {
        const stepInfo = state().steps.get(step);
        return stepInfo?.status === InitializationStatus.Completed;
      }),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error(`Timeout waiting for step ${step}`)), timeoutMs)
      )
    ]);
  };

  const getStepStatus = (step: InitializationStep): InitializationStatus => {
    return state().steps.get(step)?.status || InitializationStatus.Pending;
  };

  const isStepCompleted = (step: InitializationStep): boolean => {
    return getStepStatus(step) === InitializationStatus.Completed;
  };

  const reset = () => {
    setState({
      steps: new Map(),
      isComplete: false,
      hasErrors: false,
      startTime: Date.now()
    });
    initializeSteps();
  };

  const getNextReadyStep = (): InitializationStep | null => {
    for (const [step, stepInfo] of state().steps) {
      if (stepInfo.status === InitializationStatus.Pending && areDepencenciesMet(step)) {
        return step;
      }
    }
    return null;
  };

  // Initialize on service creation
  initializeSteps();

  return {
    state,
    executeStep,
    waitForStep,
    getStepStatus,
    isStepCompleted,
    reset,
    getNextReadyStep,
    setProgressCallback,
    setErrorCallback,
    calculateProgress,
    config
  };
}
