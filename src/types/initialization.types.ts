/**
 * Initialization state types for managing the app startup sequence
 */

export enum InitializationStep {
  // Core steps
  UserGesture = "user-gesture",
  CoreWasm = "core-wasm",
  AppState = "app-state",
  
  // Connection steps
  WebsocketIdentity = "websocket-identity",
  WebsocketConnection = "websocket-connection",
  WelcomeEvent = "welcome-event",
  ClientLoaded = "client-loaded",
  
  // Audio steps
  AudioService = "audio-service",
  SynthEngine = "synth-engine",
  ClientSocketId = "client-socket-id",
  ClientAddedToSynth = "client-added-to-synth",
  Soundfont = "soundfont",
  
  // Service steps
  AppSettings = "app-settings",
  UsersService = "users-service",
  ChatService = "chat-service",
  RoomsService = "rooms-service",
  MonitorService = "monitor-service",
  
  // Final step
  Complete = "complete"
}

export interface InitializationStepInfo {
  step: InitializationStep;
  status: InitializationStatus;
  startTime?: number;
  endTime?: number;
  error?: string;
  dependencies: InitializationStep[];
  timeout?: number;
  retryCount?: number;
  maxRetries?: number;
}

export enum InitializationStatus {
  Pending = "pending",
  InProgress = "in-progress",
  Completed = "completed",
  Failed = "failed",
  Retrying = "retrying"
}

export interface InitializationState {
  steps: Map<InitializationStep, InitializationStepInfo>;
  currentStep?: InitializationStep;
  isComplete: boolean;
  hasErrors: boolean;
  startTime: number;
}

export interface InitializationConfig {
  defaultTimeout: number;
  maxRetries: number;
  retryDelay: number;
  enableLogging: boolean;
}

export interface StepExecutor {
  execute: () => Promise<void>;
  validate?: () => Promise<boolean>;
  cleanup?: () => Promise<void>;
}

export type InitializationProgressCallback = (step: InitializationStep, status: InitializationStatus, progress: number) => void;
export type InitializationErrorCallback = (step: InitializationStep, error: string) => void;
